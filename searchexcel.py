import openpyxl
import re


def get_merged_cell_value(worksheet, row, col):
    """
    获取合并单元格的值
    如果单元格是合并单元格的一部分，返回合并单元格的值
    否则返回单元格本身的值
    """
    cell = worksheet.cell(row=row, column=col)

    # 检查是否是合并单元格
    for merged_range in worksheet.merged_cells.ranges:
        if cell.coordinate in merged_range:
            # 获取合并单元格的左上角单元格
            top_left_cell = worksheet.cell(row=merged_range.min_row, column=merged_range.min_col)
            return top_left_cell.value

    return cell.value


def search_excel_and_print_results(file_path, sheet_name, search_text, search_column='D', result_column='A'):
    """
    在Excel文件中搜索指定内容并打印结果

    参数:
    file_path: Excel文件路径
    sheet_name: 工作表名称
    search_text: 要搜索的文本
    search_column: 搜索的列（默认D列）
    result_column: 要打印的结果列（默认A列）
    """
    try:
        # 打开Excel文件
        workbook = openpyxl.load_workbook(file_path)

        # 检查工作表是否存在
        if sheet_name not in workbook.sheetnames:
            print(f"错误：工作表 '{sheet_name}' 不存在")
            print(f"可用的工作表：{workbook.sheetnames}")
            return

        worksheet = workbook[sheet_name]

        # 获取列号
        search_col_num = ord(search_column.upper()) - ord('A') + 1
        result_col_num = ord(result_column.upper()) - ord('A') + 1

        print(f"在文件 '{file_path}' 的工作表 '{sheet_name}' 中搜索...")
        print(f"搜索列：{search_column}列，搜索内容：'{search_text}'")
        print(f"结果列：{result_column}列")
        print("-" * 50)

        found_results = []

        # 遍历所有行
        for row in range(1, worksheet.max_row + 1):
            # 获取搜索列的值
            search_cell_value = get_merged_cell_value(worksheet, row, search_col_num)

            # 检查是否包含搜索文本
            if search_cell_value and search_text.lower() in str(search_cell_value).lower():
                # 获取结果列的值
                result_cell_value = get_merged_cell_value(worksheet, row, result_col_num)

                # 清理搜索值和结果值中的回车符、换行符和多余空格
                clean_search_value = ' '.join(str(search_cell_value).split()) if search_cell_value else ''
                clean_result_value = ' '.join(str(result_cell_value).split()) if result_cell_value else ''

                found_results.append({
                    'row': row,
                    'search_value': clean_search_value,
                    'result_value': clean_result_value
                })

        # 打印结果
        if found_results:
            print(f"找到 {len(found_results)} 个匹配结果：")
            print()
            for i, result in enumerate(found_results, 1):
                print(f"结果 {i}:")
                print(f"  行号: {result['row']}")
                print(f"  {search_column}列内容: {result['search_value']}")
                print(f"  {result_column}列内容: {result['result_value']}")
                print()

            # 汇总A列内容，去重并用"、"连接
            a_column_values = []
            seen_values = set()  # 用于去重

            for result in found_results:
                if result['result_value']:
                    # 彻底清理值：去除所有类型的空白字符并去除重复
                    clean_value = ' '.join(str(result['result_value']).split())
                    if clean_value and clean_value not in seen_values:
                        seen_values.add(clean_value)
                        a_column_values.append(clean_value)

            print("=" * 50)
            print("A列内容汇总：")
            if a_column_values:
                # 对每个值再次进行彻底清理
                cleaned_values = []
                for value in a_column_values:
                    # 使用正则表达式移除所有类型的空白字符
                    clean_value = re.sub(r'\s+', '', str(value))
                    if clean_value:
                        cleaned_values.append(clean_value)

                # 用"、"连接，然后再次清理整个字符串
                summary = "、".join(cleaned_values)
                # 最终清理：移除所有可能的空白字符，但保留"、"分隔符
                final_summary = re.sub(r'\s', '', summary)
                print(final_summary)
            else:
                print("无有效的A列内容")
            print("=" * 50)
        else:
            print(f"未找到包含 '{search_text}' 的内容")

        workbook.close()

    except FileNotFoundError:
        print(f"错误：文件 '{file_path}' 不存在")
    except Exception as e:
        print(f"发生错误：{e}")


def main():
    """
    主函数 - 交互式搜索
    """
    file_path = "PICU_网络安全威胁分析和风险评估表_V1.0_20240621.xlsx"
    sheet_name = "网络安全需求CS Requirement List"

    print("Excel搜索工具")
    print("=" * 50)
    print(f"文件：{file_path}")
    print(f"工作表：{sheet_name}")
    print("搜索列：D列")
    print("结果列：A列")
    print("=" * 50)

    while True:
        search_text = input("\n请输入要搜索的内容（输入 'quit' 退出）：").strip()

        if search_text.lower() == 'quit':
            print("退出程序")
            break

        if not search_text:
            print("请输入有效的搜索内容")
            continue

        search_excel_and_print_results(file_path, sheet_name, search_text)


if __name__ == "__main__":
    main()