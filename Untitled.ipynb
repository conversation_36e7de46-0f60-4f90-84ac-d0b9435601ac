{"cells": [{"cell_type": "markdown", "id": "666c24d3-2c2b-44f4-85db-13901107a487", "metadata": {}, "source": ["# 从报告提取用例"]}, {"cell_type": "code", "execution_count": 3, "id": "8f661aaa-9c09-4301-abac-50a152406c1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["找到的docx文件： ['网络安全确认测试报告_【L42P PLAN A】_【CDC】_【20250824】.docx', '网络安全确认测试报告_【LK2A L3】_【整车】_【20250922】.docx', '网络安全确认测试报告_【LK2A L6】_【整车】_【20250923】.docx']\n", "保存成功: 网络安全确认测试报告_【L42P PLAN A】_【CDC】_【20250824】_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【LK2A L3】_【整车】_【20250922】_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【LK2A L6】_【整车】_【20250923】_提取结果.xlsx\n"]}], "source": ["import os\n", "from openpyxl import Workbook\n", "from docx import Document\n", "\n", "# 获取当前目录下的所有 docx 文件\n", "docx_files = [f for f in os.listdir('.') if f.endswith('.docx')]\n", "\n", "print(\"找到的docx文件：\", docx_files)\n", "\n", "for each in docx_files:\n", "    wb = Workbook()\n", "    ws = wb.active\n", "    \n", "    # 添加表头\n", "    ws.append([\"测试用例编号\", \"测试用例名称\", \"测试输入\", \"测试工具\", \"测试步骤\"])\n", "    \n", "    # 读取文档\n", "    doc = Document(each)\n", "    \n", "    # 遍历指定范围的表格\n", "    for each_tab in doc.tables[6:-4]:\n", "        test_id = each_tab.cell(0, 1).text.strip()\n", "        testname = each_tab.cell(1, 1).text.strip()\n", "        inputdata = each_tab.cell(3, 1).text.strip()\n", "        testtool = each_tab.cell(4, 1).text.strip()\n", "        teststep = each_tab.cell(5, 1).text.strip()\n", "        \n", "        row_data = [test_id, testname, inputdata, testtool, teststep]\n", "        ws.append(row_data)\n", "    \n", "    # 保存 Excel，使用原始文件名来区分\n", "    save_name = os.path.splitext(each)[0] + \"_提取结果.xlsx\"\n", "    wb.save(save_name)\n", "    print(f\"保存成功: {save_name}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "e690a5ca-4128-4b88-9a0f-31b8ac930ca4", "metadata": {}, "outputs": [{"ename": "PackageNotFoundError", "evalue": "Package not found at 'LK1A_CDC_8295渗透测试报告V2.1.1.docx'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mPackageNotFoundError\u001b[0m                      <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 14\u001b[0m\n\u001b[0;32m     11\u001b[0m ws\u001b[38;5;241m.\u001b[39mappend([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m测试项\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m用例编号\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m用例名称\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m测试步骤\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[0;32m     13\u001b[0m \u001b[38;5;66;03m# 读取文档\u001b[39;00m\n\u001b[1;32m---> 14\u001b[0m doc \u001b[38;5;241m=\u001b[39m \u001b[43mDocument\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mLK1A_CDC_8295渗透测试报告V2.1.1.docx\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     16\u001b[0m \u001b[38;5;66;03m# 遍历指定范围的表格\u001b[39;00m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m each_tab \u001b[38;5;129;01min\u001b[39;00m doc\u001b[38;5;241m.\u001b[39mtables[\u001b[38;5;241m6\u001b[39m:\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m4\u001b[39m]:\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\docx\\api.py:27\u001b[0m, in \u001b[0;36mDocument\u001b[1;34m(docx)\u001b[0m\n\u001b[0;32m     20\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Return a |Document| object loaded from `docx`, where `docx` can be either a path\u001b[39;00m\n\u001b[0;32m     21\u001b[0m \u001b[38;5;124;03mto a ``.docx`` file (a string) or a file-like object.\u001b[39;00m\n\u001b[0;32m     22\u001b[0m \n\u001b[0;32m     23\u001b[0m \u001b[38;5;124;03mIf `docx` is missing or ``None``, the built-in default document \"template\" is\u001b[39;00m\n\u001b[0;32m     24\u001b[0m \u001b[38;5;124;03mloaded.\u001b[39;00m\n\u001b[0;32m     25\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     26\u001b[0m docx \u001b[38;5;241m=\u001b[39m _default_docx_path() \u001b[38;5;28;01mif\u001b[39;00m docx \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01<PERSON>se\u001b[39;00m docx\n\u001b[1;32m---> 27\u001b[0m document_part \u001b[38;5;241m=\u001b[39m cast(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDocumentPart\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[43mPackage\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdocx\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mmain_document_part)\n\u001b[0;32m     28\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m document_part\u001b[38;5;241m.\u001b[39mcontent_type \u001b[38;5;241m!=\u001b[39m CT\u001b[38;5;241m.\u001b[39mWML_DOCUMENT_MAIN:\n\u001b[0;32m     29\u001b[0m     tmpl \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfile \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m is not a Word file, content type is \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\docx\\opc\\package.py:126\u001b[0m, in \u001b[0;36mOpcPackage.open\u001b[1;34m(cls, pkg_file)\u001b[0m\n\u001b[0;32m    123\u001b[0m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[0;32m    124\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mopen\u001b[39m(\u001b[38;5;28mcls\u001b[39m, pkg_file: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m|\u001b[39m IO[\u001b[38;5;28mbytes\u001b[39m]) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Self:\n\u001b[0;32m    125\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Return an |OpcPackage| instance loaded with the contents of `pkg_file`.\"\"\"\u001b[39;00m\n\u001b[1;32m--> 126\u001b[0m     pkg_reader \u001b[38;5;241m=\u001b[39m \u001b[43mPackageReader\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_file\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpkg_file\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    127\u001b[0m     package \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mcls\u001b[39m()\n\u001b[0;32m    128\u001b[0m     Unmarshaller\u001b[38;5;241m.\u001b[39munmarshal(pkg_reader, package, PartFactory)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\docx\\opc\\pkgreader.py:22\u001b[0m, in \u001b[0;36mPackageReader.from_file\u001b[1;34m(pkg_file)\u001b[0m\n\u001b[0;32m     19\u001b[0m \u001b[38;5;129m@staticmethod\u001b[39m\n\u001b[0;32m     20\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mfrom_file\u001b[39m(pkg_file):\n\u001b[0;32m     21\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Return a |PackageReader| instance loaded with contents of `pkg_file`.\"\"\"\u001b[39;00m\n\u001b[1;32m---> 22\u001b[0m     phys_reader \u001b[38;5;241m=\u001b[39m \u001b[43mPhysPkgReader\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpkg_file\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     23\u001b[0m     content_types \u001b[38;5;241m=\u001b[39m _ContentTypeMap\u001b[38;5;241m.\u001b[39mfrom_xml(phys_reader\u001b[38;5;241m.\u001b[39mcontent_types_xml)\n\u001b[0;32m     24\u001b[0m     pkg_srels \u001b[38;5;241m=\u001b[39m PackageReader\u001b[38;5;241m.\u001b[39m_srels_for(phys_reader, PACKAGE_URI)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\docx\\opc\\phys_pkg.py:21\u001b[0m, in \u001b[0;36mPhysPkgReader.__new__\u001b[1;34m(cls, pkg_file)\u001b[0m\n\u001b[0;32m     19\u001b[0m         reader_cls \u001b[38;5;241m=\u001b[39m _ZipPkgReader\n\u001b[0;32m     20\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m---> 21\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m PackageNotFoundError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPackage not found at \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m pkg_file)\n\u001b[0;32m     22\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:  \u001b[38;5;66;03m# assume it's a stream and pass it to Zip reader to sort out\u001b[39;00m\n\u001b[0;32m     23\u001b[0m     reader_cls \u001b[38;5;241m=\u001b[39m _ZipPkgReader\n", "\u001b[1;31mPackageNotFoundError\u001b[0m: Package not found at 'LK1A_CDC_8295渗透测试报告V2.1.1.docx'"]}], "source": ["import os\n", "from openpyxl import Workbook\n", "from docx import Document\n", "\n", "# 获取当前目录下的所有 docx 文件\n", "\n", "wb = Workbook()\n", "ws = wb.active\n", "\n", "# 添加表头\n", "ws.append([\"测试项\",\"用例编号\", \"用例名称\", \"测试步骤\"])\n", "\n", "# 读取文档\n", "doc = Document('LK1A_CDC_8295渗透测试报告V2.1.1.docx')\n", "\n", "# 遍历指定范围的表格\n", "for each_tab in doc.tables[6:-4]:\n", "    testItem = each_tab.cell(2, 1).text.strip()\n", "    test_id = each_tab.cell(0, 1).text.strip()\n", "    testname = each_tab.cell(1, 1).text.strip()\n", "    testtool = each_tab.cell(4, 1).text.strip()\n", "\n", "    \n", "    row_data = [testItem, test_id, testname, testtool]\n", "    ws.append(row_data)\n", "\n", "# 保存 Excel，使用原始文件名来区分\n", "\n", "wb.save(\"LK1A_CDC_8295渗透测试报告V2.1.1_提取结果.xlsx\")\n", "print(f\"保存成功:\")"]}, {"cell_type": "code", "execution_count": 5, "id": "e693f92c-e81f-4970-9236-668cd078a2f3", "metadata": {}, "outputs": [], "source": ["doc = Document('LK1A_CDC_8295渗透测试报告V2.1.1.docx')"]}, {"cell_type": "code", "execution_count": 12, "id": "e5d6e20c-e96a-4a20-bcd3-2f8e8d0ea11f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'PT-DS-006'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["doc.tables[-5].cell(0, 1).text"]}, {"cell_type": "code", "execution_count": 1, "id": "cf84f78e-d683-4dcb-b23c-8e4a2388ba15", "metadata": {}, "outputs": [], "source": ["import os\n", "from openpyxl import Workbook\n", "from docx import Document\n"]}, {"cell_type": "code", "execution_count": 12, "id": "f5abb466-78d6-4a72-8aa8-f000717670d7", "metadata": {}, "outputs": [], "source": ["doc = Document('./Fr_camera/网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】.docx')"]}, {"cell_type": "code", "execution_count": 13, "id": "aafda424-37d9-43de-9c2a-b41e629f64ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["版本\t测试人员\t测试日期\t修订内容\t审核人员\t审核日期\t\n", "\n", "V1.0\t王黎\t2025/06/23-2025/07/04\t报告编制\t\t\t\n", "\n", "V1.1\t王黎\t2025/07/09-2025/07/11\t报告内容\t\t\t\n", "\n", "V2.0\t夏浪国\t2025/08/28-2025/08/29\t复测修改报告\t\t\t\n", "\n"]}], "source": ["for r in range(len(doc.tables[1].rows)):\n", "    for c in range(len(doc.tables[1].columns)):\n", "        print(doc.tables[1].cell(r,c).text,end = '\\t')\n", "    print(\"\\n\")\n", "        "]}, {"cell_type": "code", "execution_count": 14, "id": "560b91e9-8f1d-4085-9faa-f0d76bfe68dc", "metadata": {}, "outputs": [{"ename": "IndexError", "evalue": "list index out of range", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mdoc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtables\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcell\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m4\u001b[39;49m\u001b[43m,\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mtext\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\docx\\table.py:91\u001b[0m, in \u001b[0;36mTable.cell\u001b[1;34m(self, row_idx, col_idx)\u001b[0m\n\u001b[0;32m     86\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"|_Cell| at `row_idx`, `col_idx` intersection.\u001b[39;00m\n\u001b[0;32m     87\u001b[0m \n\u001b[0;32m     88\u001b[0m \u001b[38;5;124;03m(0, 0) is the top, left-most cell.\u001b[39;00m\n\u001b[0;32m     89\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     90\u001b[0m cell_idx \u001b[38;5;241m=\u001b[39m col_idx \u001b[38;5;241m+\u001b[39m (row_idx \u001b[38;5;241m*\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_column_count)\n\u001b[1;32m---> 91\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_cells\u001b[49m\u001b[43m[\u001b[49m\u001b[43mcell_idx\u001b[49m\u001b[43m]\u001b[49m\n", "\u001b[1;31mIndexError\u001b[0m: list index out of range"]}], "source": ["doc.tables[1].cell(4,1).text"]}, {"cell_type": "code", "execution_count": 7, "id": "556719da-4f67-4319-9f2c-2331537cf4c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["59"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(doc.tables[5].rows)"]}, {"cell_type": "code", "execution_count": 15, "id": "582889ef-9427-486d-a37a-bf91660a7f60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["段落 1: \n", "段落 2: \n", "段落 3: \n", "段落 4: \n", "段落 5: \n", "段落 6: \n", "段落 7: \n", "段落 8: \n", "段落 9: \n", "段落 10: \n", "段落 11: \n", "段落 12: \n", "段落 13: \n", "段落 14: \n", "段落 15: \n", "段落 16: \n", "段落 17: \n", "段落 18: \n", "段落 19: \n", "段落 20: \t\n", "段落 21: \n", "段落 22: \n", "段落 23: \n", "段落 24: \n", "段落 25: \n", "段落 26: 目的\n", "段落 27: 该文档目的为测试LK2A车型项目Fr_camera是否符合网络安全目标全部要求。\n", "段落 28: 测试工具\n", "段落 29: 进行Fr_camera确认测试时至少需准备如下测试工具：\n", "段落 30: \n", "段落 31: \n", "段落 32: 测试准备\n", "段落 33: 测试输入\n", "段落 34: 进行Fr_camaera确认测试时需准备如下测试资源：\n", "段落 35: \n", "段落 36: 版本信息\n", "段落 37: 测试结果汇总\n", "段落 38: 测试总结\n", "段落 39: 广东为辰信息科技有限公司 受 东风汽车有限公司 的委托，于 2025年6月23日 至 2025年7月11日 对“ LK2A Fr_camera ”的安全等级进行评测判定。\n", "段落 40: 受测系统在第一轮信息安全测试过程中发现漏洞情况如下：\n", "段落 41: 超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 0 个，低危漏洞 1 个。\n", "段落 42: 根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统 。\n", "段落 43: \n", "段落 44: 测试结果\n", "段落 45: 硬件安全测试\n", "段落 46: 丝印信息泄露（低危）\n", "段落 47: 调试接口暴露测试（安全）\n", "段落 48: 调试接口安全防护测试（安全）\n", "段落 49: 调试接口固件提取测试（安全）\n", "段落 50: emmc芯片固件提取（N/T）\n", "段落 51: 系统安全\n", "段落 52: 第三方应用安装测试（安全）\n", "段落 53: 反弹shell测试（安全）\n", "段落 54: ADB提权测试（安全）\n", "段落 55: 内核提权漏洞（N/A）\n", "段落 56: suid提权漏洞（N/A）\n", "段落 57: root软件提权（N/A）\n", "段落 58: logcat日志安全测试（N/A）\n", "段落 59: 系统服务与后门检测（N/A）\n", "段落 60: 系统漏洞扫描（安全）\n", "段落 61: 工程模式安全防护（N/A）\n", "段落 62: 系统组件安全（N/A）\n", "段落 63: webview安全（N/A）\n", "段落 64: JDWP安全测试（安全）\n", "段落 65: 安全启动开启（N/A）\n", "段落 66: 防火墙配置测试（N/A）\n", "段落 67: 驱动配置安全测试（N/A）\n", "段落 68: 网络接口配置测试（N/A）\n", "段落 69: 终端命令执行权限测试（N/A）\n", "段落 70: CAN总线安全（CAN1）\n", "段落 71: 多路CAN信息汇总（此项仅用于汇总多路CAN信息，不作为安全评估项）\n", "段落 72: CAN总线逆向测试（安全）\n", "段落 73: CAN总线重放测试（安全）\n", "段落 74: CAN总线拒绝服务攻击（安全）\n", "段落 75: CAN总线模糊测试（正常）\n", "段落 76: XCP协议扫描测试（安全）\n", "段落 77: CAN总线安全（CAN3）\n", "段落 78: CAN总线逆向测试（安全）\n", "段落 79: CAN总线重放测试（安全）\n", "段落 80: CAN总线拒绝服务攻击（安全）\n", "段落 81: CAN总线模糊测试（正常）\n", "段落 82: XCP协议扫描测试（安全）\n", "段落 83: CAN总线安全（CAN5）\n", "段落 84: CAN总线逆向测试（安全）\n", "段落 85: CAN总线重放测试（安全）\n", "段落 86: CAN总线拒绝服务攻击（安全）\n", "段落 87: CAN总线模糊测试（正常）\n", "段落 88: XCP协议扫描测试（安全）\n", "段落 89: 诊断安全（CAN）\n", "段落 90: 研发生产调试命令去除测试（安全）\n", "段落 91: 诊断复位测试（安全）\n", "段落 92: 种子随机度安全测试（安全）（已修复）\n", "段落 93: 读写服务安全（安全）\n", "段落 94: 2F服务安全（安全）\n", "段落 95: 27服务安全（安全）（已修复）\n", "段落 96: 31服务安全（安全）\n", "段落 97: 诊断安全（DoIP）\n", "段落 98: 研发生产调试命令去除测试（安全）\n", "段落 99: 诊断复位测试（安全）（已修复）\n", "段落 100: 种子随机度安全测试（安全）（已修复）\n", "段落 101: 读写服务安全（安全）\n", "段落 102: 2F服务安全（安全）\n", "段落 103: 27服务安全（安全）\n", "段落 104: 31服务安全（安全）\n", "段落 105: 以太网安全\n", "段落 106: 以太网拒绝服务测试（安全）\n", "段落 107: 以太网端口扫描（安全）\n", "段落 108: 以太网危险端口测试（安全）\n", "段落 109: DoIP模糊测试（正常）\n", "段落 110: SOME/IP模糊测试（正常）\n", "段落 111: 以太网设备传输安全（N/A）\n", "段落 112: \n", "段落 113: 附录A：网络安全等级评定\t\n", "段落 114: 智能汽车网络安全等级ACSL（AutomotiveCyber Security Level）分为五个等级，分别为S0-S4,由前缀“S”加由低向高递增的数字表示安全的递减情况，表达方式与ISO26262的表达方式一致。\n", "段落 115: 智能汽车网络安全等级评定通过确认测试活动评定的漏洞危害等级（VL）分布情况评估得出。其换算方法参见下表：\n", "段落 116: \n", "段落 117: 附录B：漏洞危害程度分级\n", "段落 118: 评分原则\n", "段落 119: 评分由三个基本尺度组成：基本分，时间分，环境分，如图2所示：\n", "段落 120: \n", "段落 121: 图 2评分基本尺度组成图\n", "段落 122: 基本分（Base）：代表着漏洞的原始属性，不受时间与环境的影响，又由Exploitability可执行性与影响程度Impact 度量。基本评分原则有以下几条：\n", "段落 123: 每个安全漏洞必须是单独评估，不能考虑与其他安全漏洞的交叉影响。\n", "段落 124: 只考虑漏洞的直接影响，不考虑间接影响。\n", "段落 125: 按照通常使用的权限来评估漏洞的影响。\n", "段落 126: 按照漏洞的最大影响来打分。\n", "段落 127: 时间分（Temporal）：反应漏洞随着时间推移的影响而不受环境影响，举个简单的例子，随着一个漏洞软件的补丁不断增加，该漏洞的CVSS分数会随之减少。\n", "段落 128: 环境分（Environmental）：代表特定环境下执行漏洞的分数，允许根据相应业务需求提高或者降低该分值。通常环境分需要最终用户进行评估和计算。\n", "段落 129: 评分指标\n", "段落 130: 评分字段用于以简洁的形式记录或传输CVSS定量信息，评分字段应尽量按照下表所示顺序进行评分。其中基础分为必选项，时间分和环境分为可选项。\n", "段落 131: \n", "段落 132: 评分量化值\n", "段落 133: 每个衡量值（Metric Value）都有一个相关的常数（Numerical Value），该常数在公式中使用。计算公式和指标权重详情请参见附件《CVSS得分计算公式》。\n", "段落 134: 漏洞评分与等级\n", "段落 135: 漏洞的评分范围为0-10.0，分为超危（critical）、高（high）、中（medium）、低（low）、无（none）五个级别。下表是评分范围与等级映射关系表。\n", "段落 136: \n"]}], "source": ["for i, para in enumerate(doc.paragraphs):\n", "    print(f\"段落 {i+1}: {para.text}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c5e829d4-0c76-4215-851f-d0004840adee", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}