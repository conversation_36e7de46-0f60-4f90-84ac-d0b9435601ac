测试数据对比结果报告
生成时间: 2025-09-24 10:22:34
处理文件夹数量: 3
文件夹列表: L42P PLAN A_CDC, LK2A_L3, LK2A_L6
================================================================================


处理文件夹: L42P PLAN A_CDC
Word文件: 网络安全确认测试报告_【L42P PLAN A】_【CDC】_【20250824】.docx
Excel文件: 网络安全确认测试用例_【L42P PLAN A】_【CDC】_【20250824】.xlsx
测试报告汇总数据量: 81
测试过程数据量: 81
测试用例数据量: 81

=== 处理文件夹: L42P PLAN A_CDC ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 3 个，低危漏洞 5 个。
受测系统在第二轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 1 个，低危漏洞 3 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S2（B）级，中安全隐患系统 。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S2（B）级，中安全隐患系统 。

检测到的测试轮次: ['一', '二']
检测到的漏洞统计: [('0', '0', '3', '5'), ('0', '0', '1', '3')]
最终漏洞统计: 超高危0个，高危0个，中危1个，低危3个
报告中的安全等级: S2（B）级，，中安全隐患系统
根据漏洞统计计算的等级: S2级，中安全隐患
漏洞统计信息正确

处理文件夹: LK2A_L3
Word文件: 网络安全确认测试报告_【LK2A L3】_【整车】_【20250922】.docx
Excel文件: 网络安全确认测试用例_【LK2A L3】_【整车】_【20250922】.xlsx
测试报告汇总数据量: 85
测试过程数据量: 85
测试用例数据量: 85

=== 处理文件夹: LK2A_L3 ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 3 个，中危漏洞 5 个，低危漏洞 9 个。
受测系统在第二轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 1 个，低危漏洞 0 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S2（B）级，中安全隐患系统 。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S2（B）级，中安全隐患系统 。

检测到的测试轮次: ['一', '二']
检测到的漏洞统计: [('0', '3', '5', '9'), ('0', '0', '1', '0')]
最终漏洞统计: 超高危0个，高危0个，中危1个，低危0个
报告中的安全等级: S2（B）级，，中安全隐患系统
根据漏洞统计计算的等级: S2级，中安全隐患
漏洞统计信息正确

处理文件夹: LK2A_L6
Word文件: 网络安全确认测试报告_【LK2A L6】_【整车】_【20250923】.docx
Excel文件: 网络安全确认测试用例_【LK2A L6】_【整车】_【20250922】.xlsx
测试报告汇总数据量: 47
测试过程数据量: 47
测试用例数据量: 47

=== 处理文件夹: LK2A_L6 ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 1 个，中危漏洞 1 个，低危漏洞 12 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S3（C）级，不安全系统 。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S3（C）级，不安全系统 。

检测到的测试轮次: ['一']
检测到的漏洞统计: [('0', '1', '1', '12')]
最终漏洞统计: 超高危0个，高危1个，中危1个，低危12个
报告中的安全等级: S3（C）级，，不安全系统
根据漏洞统计计算的等级: S3级，不安全
漏洞统计信息正确

==================================================
总结报告
==================================================
所有文件夹的数据都一致！

报告生成完成！
详细结果已保存到: 测试数据对比结果_20250924_102234.txt
