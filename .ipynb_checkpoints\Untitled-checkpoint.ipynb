{"cells": [{"cell_type": "markdown", "id": "666c24d3-2c2b-44f4-85db-13901107a487", "metadata": {}, "source": ["# 从报告提取用例"]}, {"cell_type": "code", "execution_count": 3, "id": "8f661aaa-9c09-4301-abac-50a152406c1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["找到的docx文件： ['010-5 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】.docx', '010-5 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】.docx', 'LK1A_CDC_8295渗透测试报告V2.1.1.docx', '网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】.docx', '网络安全确认测试报告_【PK1B L2】_【整车】_【20250904】.docx', '网络安全确认测试报告_【PK1B L4】_【整车】_【20250904】.docx', '网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】.docx']\n", "保存成功: 010-5 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】_提取结果.xlsx\n", "保存成功: 010-5 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】_提取结果.xlsx\n", "保存成功: LK1A_CDC_8295渗透测试报告V2.1.1_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【PK1B L2】_【整车】_【20250904】_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【PK1B L4】_【整车】_【20250904】_提取结果.xlsx\n", "保存成功: 网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】_提取结果.xlsx\n"]}], "source": ["import os\n", "from openpyxl import Workbook\n", "from docx import Document\n", "\n", "# 获取当前目录下的所有 docx 文件\n", "docx_files = [f for f in os.listdir('.') if f.endswith('.docx')]\n", "\n", "print(\"找到的docx文件：\", docx_files)\n", "\n", "for each in docx_files:\n", "    wb = Workbook()\n", "    ws = wb.active\n", "    \n", "    # 添加表头\n", "    ws.append([\"测试用例编号\", \"测试用例名称\", \"测试输入\", \"测试工具\", \"测试步骤\"])\n", "    \n", "    # 读取文档\n", "    doc = Document(each)\n", "    \n", "    # 遍历指定范围的表格\n", "    for each_tab in doc.tables[6:-4]:\n", "        test_id = each_tab.cell(0, 1).text.strip()\n", "        testname = each_tab.cell(1, 1).text.strip()\n", "        inputdata = each_tab.cell(3, 1).text.strip()\n", "        testtool = each_tab.cell(4, 1).text.strip()\n", "        teststep = each_tab.cell(5, 1).text.strip()\n", "        \n", "        row_data = [test_id, testname, inputdata, testtool, teststep]\n", "        ws.append(row_data)\n", "    \n", "    # 保存 Excel，使用原始文件名来区分\n", "    save_name = os.path.splitext(each)[0] + \"_提取结果.xlsx\"\n", "    wb.save(save_name)\n", "    print(f\"保存成功: {save_name}\")"]}, {"cell_type": "code", "execution_count": 16, "id": "e690a5ca-4128-4b88-9a0f-31b8ac930ca4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["保存成功:\n"]}], "source": ["import os\n", "from openpyxl import Workbook\n", "from docx import Document\n", "\n", "# 获取当前目录下的所有 docx 文件\n", "\n", "wb = Workbook()\n", "ws = wb.active\n", "\n", "# 添加表头\n", "ws.append([\"测试项\",\"用例编号\", \"用例名称\", \"测试步骤\"])\n", "\n", "# 读取文档\n", "doc = Document('LK1A_CDC_8295渗透测试报告V2.1.1.docx')\n", "\n", "# 遍历指定范围的表格\n", "for each_tab in doc.tables[6:-4]:\n", "    testItem = each_tab.cell(2, 1).text.strip()\n", "    test_id = each_tab.cell(0, 1).text.strip()\n", "    testname = each_tab.cell(1, 1).text.strip()\n", "    testtool = each_tab.cell(4, 1).text.strip()\n", "\n", "    \n", "    row_data = [testItem, test_id, testname, testtool]\n", "    ws.append(row_data)\n", "\n", "# 保存 Excel，使用原始文件名来区分\n", "\n", "wb.save(\"LK1A_CDC_8295渗透测试报告V2.1.1_提取结果.xlsx\")\n", "print(f\"保存成功:\")"]}, {"cell_type": "code", "execution_count": 5, "id": "e693f92c-e81f-4970-9236-668cd078a2f3", "metadata": {}, "outputs": [], "source": ["doc = Document('LK1A_CDC_8295渗透测试报告V2.1.1.docx')"]}, {"cell_type": "code", "execution_count": 12, "id": "e5d6e20c-e96a-4a20-bcd3-2f8e8d0ea11f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'PT-DS-006'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["doc.tables[-5].cell(0, 1).text"]}, {"cell_type": "code", "execution_count": 1, "id": "cf84f78e-d683-4dcb-b23c-8e4a2388ba15", "metadata": {}, "outputs": [], "source": ["import os\n", "from openpyxl import Workbook\n", "from docx import Document\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f5abb466-78d6-4a72-8aa8-f000717670d7", "metadata": {}, "outputs": [], "source": ["doc = Document('./LK2A_TCU/网络安全确认测试报告_【LK2A】_【TCU】_【20250904】.docx')"]}, {"cell_type": "code", "execution_count": null, "id": "aafda424-37d9-43de-9c2a-b41e629f64ab", "metadata": {}, "outputs": [], "source": ["for r in range(len(doc.tables[1].rows)):\n", "    for l in range(len(doc.tables[1].columns)):\n", "        print(doc.tables[1].cell(r,c).text,end = '\\t')\n", "    print(\"\\n\")\n", "        "]}, {"cell_type": "code", "execution_count": 5, "id": "560b91e9-8f1d-4085-9faa-f0d76bfe68dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["'测试人员'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["doc.tables[1]"]}, {"cell_type": "code", "execution_count": 7, "id": "556719da-4f67-4319-9f2c-2331537cf4c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["59"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(doc.tables[5].rows)"]}, {"cell_type": "code", "execution_count": null, "id": "582889ef-9427-486d-a37a-bf91660a7f60", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}