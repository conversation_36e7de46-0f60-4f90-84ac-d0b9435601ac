import os
import pandas as pd
from docx import Document
from datetime import datetime
import re

def extract_test_summary_data(doc_path, folder_name):
    """提取Word文档测试报告汇总数据"""
    try:
        doc = Document(doc_path)
        test_summary_data = {}

        # 针对LK1A_CDC文件夹的特殊处理
        if folder_name == "LK1A_CDC":
            if len(doc.tables) < 3:
                print(f"警告: {doc_path} 表格数量不足，无法提取表格2")
                return {}

            table = doc.tables[2]  # 第三个表格（索引为2）

            # 从第二行开始提取（第一行是表头）
            for row in table.rows[1:]:
                if len(row.cells) >= 4:  # 确保有足够的列
                    test_id = row.cells[1].text.strip()    # 第二列是用例编号
                    test_name = row.cells[3].text.strip()  # 第四列是测试用例名称
                    if test_id and test_name:  # 确保不是空值
                        test_summary_data[test_id] = test_name
        else:
            # 其他文件夹的默认处理
            if len(doc.tables) < 6:
                print(f"警告: {doc_path} 表格数量不足，无法提取表格5")
                return {}

            table = doc.tables[5]  # 第六个表格（索引为5）

            # 从第二行开始提取（第一行是表头）
            for row in table.rows[1:]:
                if len(row.cells) >= 2:
                    test_id = row.cells[0].text.strip()   # 第一列是用例编号
                    test_name = row.cells[1].text.strip() # 第二列是测试用例名称
                    if test_id and test_name:  # 确保不是空值
                        test_summary_data[test_id] = test_name

        return test_summary_data
    except Exception as e:
        print(f"提取Word文档测试报告汇总数据时出错 {doc_path}: {e}")
        return {}

def extract_test_process_data(doc_path, folder_name):
    """提取Word文档测试过程数据"""
    try:
        doc = Document(doc_path)
        test_process_data = {}

        # 针对LK1A_CDC文件夹的特殊处理
        if folder_name == "LK1A_CDC":
            if len(doc.tables) < 7:
                print(f"警告: {doc_path} 表格数量不足，无法提取表格6到-4")
                return {}

            # 提取表格6到-4范围的数据，但使用不同的单元格位置
            for each_tab in doc.tables[6:-4]:
                if len(each_tab.rows) >= 2 and len(each_tab.rows[0].cells) >= 2 and len(each_tab.rows[1].cells) >= 2:
                    test_id = each_tab.cell(0, 1).text.strip()   # 第1行第2列
                    test_name = each_tab.cell(1, 1).text.strip() # 第2行第2列
                    if test_id and test_name:  # 确保不是空值
                        test_process_data[test_id] = test_name
        else:
            # 其他文件夹的默认处理
            if len(doc.tables) < 7:
                print(f"警告: {doc_path} 表格数量不足，无法提取表格6到-4")
                return {}

            # 提取表格6到-4范围的数据
            for each_tab in doc.tables[6:-4]:
                if len(each_tab.rows) >= 2 and len(each_tab.rows[0].cells) >= 2 and len(each_tab.rows[1].cells) >= 2:
                    test_id = each_tab.cell(0, 1).text.strip()
                    test_name = each_tab.cell(1, 1).text.strip()
                    if test_id and test_name:  # 确保不是空值
                        test_process_data[test_id] = test_name

        return test_process_data
    except Exception as e:
        print(f"提取Word文档测试过程数据时出错 {doc_path}: {e}")
        return {}

def extract_test_case_data(excel_path):
    """提取Excel文件的数据 (测试用例)"""
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        test_case_data = {}

        # 检查列数是否足够
        if df.shape[1] < 4:
            print(f"警告: {excel_path} 列数不足，无法提取第3、4列")
            return {}

        # 第三列和第四列分别是测试用例编号和测试用例名称（索引2和3）
        for _, row in df.iterrows():
            test_id = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ""
            test_name = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""
            if test_id and test_name and test_id != 'nan':  # 确保不是空值或NaN
                test_case_data[test_id] = test_name

        return test_case_data
    except Exception as e:
        print(f"提取Excel文件数据时出错 {excel_path}: {e}")
        return {}

def check_version_and_date(doc_path, folder_name, output_file):
    """检查封面修订页的版本号和日期格式"""
    try:
        doc = Document(doc_path)
        if len(doc.tables) < 2:
            error_msg = f"警告: {doc_path} 表格数量不足，无法检查封面修订页\n"
            print(error_msg.strip())
            output_file.write(error_msg)
            return

        table = doc.tables[1]  # 封面修订页表格
        issues = []

        # 从第二行开始检查（第一行是表头）
        for r in range(1, len(table.rows)):
            row = table.rows[r]
            if len(row.cells) >= 3:
                version_cell = row.cells[0].text.strip()
                date_cell = row.cells[2].text.strip()

                # 跳过空行
                if not version_cell and not date_cell:
                    continue

                # 检查版本号格式 (Vx.x)
                if version_cell:
                    version_pattern = r'^V\d+\.\d+$'
                    if not re.match(version_pattern, version_cell):
                        issues.append(f"第{r+1}行版本号格式错误: '{version_cell}' (应为Vx.x格式)")

                # 检查日期格式 (xxxx/xx/xx-xxxx/xx/xx)
                if date_cell:
                    date_pattern = r'^\d{4}/\d{1,2}/\d{1,2}-\d{4}/\d{1,2}/\d{1,2}$'
                    if not re.match(date_pattern, date_cell):
                        issues.append(f"第{r+1}行日期格式错误: '{date_cell}' (应为xxxx/xx/xx-xxxx/xx/xx格式)")

        # 输出检查结果
        if issues:
            result_text = f"封面修订页格式问题:\n"
            for issue in issues:
                result_text += f"  - {issue}\n"
            print(result_text.strip())
            output_file.write(result_text)
        else:
            result_text = f"封面修订页格式正确\n"
            print(result_text.strip())
            output_file.write(result_text)

    except Exception as e:
        error_msg = f"检查封面修订页时出错 {doc_path}: {e}\n"
        print(error_msg.strip())
        output_file.write(error_msg)

def check_vulnerability_statistics(doc_path, folder_name, output_file):
    """检查漏洞统计信息和安全等级"""
    try:
        doc = Document(doc_path)
        issues = []

        # 安全等级规则
        security_levels = {
            'S0': {'name': '无安全隐患', 'condition': lambda stats: all(v == 0 for v in stats.values())},
            'S1': {'name': '低安全隐患', 'condition': lambda stats: stats['超高危'] == 0 and stats['高危'] == 0 and stats['中危'] == 0 and stats['低危'] > 0},
            'S2': {'name': '中安全隐患', 'condition': lambda stats: stats['超高危'] == 0 and stats['高危'] == 0 and stats['中危'] > 0},
            'S3': {'name': '不安全', 'condition': lambda stats: stats['超高危'] == 0 and stats['高危'] > 0},
            'S4': {'name': '极不安全', 'condition': lambda stats: stats['超高危'] > 0}
        }

        if folder_name == "LK1A_CDC":
            # LK1A_CDC特殊处理：从表格中提取
            if len(doc.tables) < 2:
                error_msg = f"警告: {doc_path} 表格数量不足，无法检查漏洞统计\n"
                print(error_msg.strip())
                output_file.write(error_msg)
                return

            content = doc.tables[1].cell(4, 1).text
        else:
            # 其他文件夹：从段落中提取
            content = ""
            collect_next = False
            for para in doc.paragraphs:
                para_text = para.text.strip()

                # 如果包含关键词，开始收集
                if "轮信息安全测试过程中发现漏洞情况" in para_text:
                    content += para_text + "\n"
                    collect_next = True
                    continue

                # 如果前面遇到了漏洞情况，继续收集后续段落
                if collect_next:
                    if ("超高危漏洞" in para_text or "高危漏洞" in para_text or
                        "中危漏洞" in para_text or "低危漏洞" in para_text or
                        "安全等级判定" in para_text or "受测试系统为" in para_text):
                        content += para_text + "\n"
                    elif para_text == "":
                        # 空段落继续收集
                        continue
                    elif len(para_text) > 10 and ("测试" not in para_text):
                        # 如果是长段落且不包含测试相关内容，可能已经结束收集
                        break

                # 直接包含安全等级判定的段落
                if "安全等级判定" in para_text or "受测试系统为" in para_text:
                    content += para_text + "\n"

        if not content:
            error_msg = f"未找到漏洞统计信息\n"
            print(error_msg.strip())
            output_file.write(error_msg)
            return

        # 打印提取到的内容用于调试
        debug_info = f"提取到的漏洞统计内容:\n{content}\n"
        print(debug_info.strip())
        output_file.write(debug_info)

        # 提取轮次信息
        round_pattern = r'第([一二三四五六七八九十\d]+)轮'
        rounds = re.findall(round_pattern, content)

        # 打印轮次信息
        rounds_info = f"检测到的测试轮次: {rounds}\n"
        print(rounds_info.strip())
        output_file.write(rounds_info)

        # 检查轮次是否正确
        if len(rounds) < 1:
            issues.append("未找到测试轮次信息")
        else:
            # 检查是否有错误的轮次（如第三轮等）
            valid_rounds = ['一', '二', '1', '2']
            for round_num in rounds:
                if round_num not in valid_rounds:
                    issues.append(f"发现异常测试轮次: 第{round_num}轮")

        # 提取漏洞数量
        vulnerability_pattern = r'超高危漏洞\s*(\d+)\s*个.*?高危漏洞\s*(\d+)\s*个.*?中危漏洞\s*(\d+)\s*个.*?低危漏洞\s*(\d+)\s*个'
        vuln_matches = re.findall(vulnerability_pattern, content, re.DOTALL)

        # 打印漏洞匹配信息
        vuln_info = f"检测到的漏洞统计: {vuln_matches}\n"
        print(vuln_info.strip())
        output_file.write(vuln_info)

        if not vuln_matches:
            issues.append("未找到漏洞统计数据")
        else:
            # 取最后一轮的漏洞统计（通常是最终结果）
            last_round_stats = vuln_matches[-1]
            final_stats = {
                '超高危': int(last_round_stats[0]),
                '高危': int(last_round_stats[1]),
                '中危': int(last_round_stats[2]),
                '低危': int(last_round_stats[3])
            }

            # 打印最终漏洞统计
            final_stats_info = f"最终漏洞统计: 超高危{final_stats['超高危']}个，高危{final_stats['高危']}个，中危{final_stats['中危']}个，低危{final_stats['低危']}个\n"
            print(final_stats_info.strip())
            output_file.write(final_stats_info)

            # 提取安全等级
            level_pattern = r'(S[0-4])\s*（([A-D\-])\）\s*级.*?([^。]*安全[^。]*系统)'
            level_match = re.search(level_pattern, content)

            if level_match:
                reported_level = level_match.group(1)
                reported_grade = level_match.group(2)
                reported_desc = level_match.group(3).strip()

                # 打印报告中的安全等级
                reported_info = f"报告中的安全等级: {reported_level}（{reported_grade}）级，{reported_desc}\n"
                print(reported_info.strip())
                output_file.write(reported_info)

                # 根据漏洞统计确定正确的安全等级
                correct_level = None
                for level, info in security_levels.items():
                    if info['condition'](final_stats):
                        correct_level = level
                        break

                # 打印计算出的正确等级
                if correct_level:
                    correct_info = f"根据漏洞统计计算的等级: {correct_level}级，{security_levels[correct_level]['name']}\n"
                    print(correct_info.strip())
                    output_file.write(correct_info)

                if correct_level != reported_level:
                    issues.append(f"安全等级不匹配: 报告显示{reported_level}级，根据漏洞统计应为{correct_level}级")
                    issues.append(f"漏洞统计: 超高危{final_stats['超高危']}个，高危{final_stats['高危']}个，中危{final_stats['中危']}个，低危{final_stats['低危']}个")
            else:
                issues.append("未找到安全等级信息")

        # 输出检查结果
        if issues:
            result_text = f"漏洞统计检查问题:\n"
            for issue in issues:
                result_text += f"  - {issue}\n"
            print(result_text.strip())
            output_file.write(result_text)
        else:
            result_text = f"漏洞统计信息正确\n"
            print(result_text.strip())
            output_file.write(result_text)

    except Exception as e:
        error_msg = f"检查漏洞统计时出错 {doc_path}: {e}\n"
        print(error_msg.strip())
        output_file.write(error_msg)

def compare_data(test_summary_data, test_process_data, test_case_data, folder_name, output_file):
    """对比三组数据的一致性"""
    result_text = f"\n=== 处理文件夹: {folder_name} ===\n"
    print(result_text.strip())
    output_file.write(result_text)

    # 获取所有用例编号的并集
    all_test_ids = set(test_summary_data.keys()) | set(test_process_data.keys()) | set(test_case_data.keys())

    inconsistencies = []

    for test_id in all_test_ids:
        summary_name = test_summary_data.get(test_id, "缺失")
        process_name = test_process_data.get(test_id, "缺失")
        case_name = test_case_data.get(test_id, "缺失")

        # 检查测试报告汇总与测试过程是否一致
        if summary_name != process_name:
            inconsistencies.append({
                'comparison': '测试报告汇总 vs 测试过程',
                'test_id': test_id,
                'summary_name': summary_name,
                'process_name': process_name
            })

        # 检查测试报告汇总与测试用例是否一致
        if summary_name != case_name:
            inconsistencies.append({
                'comparison': '测试报告汇总 vs 测试用例',
                'test_id': test_id,
                'summary_name': summary_name,
                'case_name': case_name
            })

        # 检查测试过程与测试用例是否一致
        if process_name != case_name:
            inconsistencies.append({
                'comparison': '测试过程 vs 测试用例',
                'test_id': test_id,
                'process_name': process_name,
                'case_name': case_name
            })

    # 打印和写入不一致的结果
    if inconsistencies:
        result_text = "发现不一致项:\n"
        print(result_text.strip())
        output_file.write(result_text)

        for item in inconsistencies:
            item_text = f"  对比: {item['comparison']}\n"
            item_text += f"  用例编号: {item['test_id']}\n"

            if 'summary_name' in item and 'process_name' in item:
                item_text += f"  测试报告汇总名称: {item['summary_name']}\n"
                item_text += f"  测试过程名称: {item['process_name']}\n"
            elif 'summary_name' in item and 'case_name' in item:
                item_text += f"  测试报告汇总名称: {item['summary_name']}\n"
                item_text += f"  测试用例名称: {item['case_name']}\n"
            elif 'process_name' in item and 'case_name' in item:
                item_text += f"  测试过程名称: {item['process_name']}\n"
                item_text += f"  测试用例名称: {item['case_name']}\n"

            item_text += "  ---\n"

            print(item_text.strip())
            output_file.write(item_text)
    else:
        result_text = "  所有数据一致！\n"
        print(result_text.strip())
        output_file.write(result_text)

    return inconsistencies

def main():
    """主函数"""
    current_dir = "."

    # 创建输出文件名（包含时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"测试数据对比结果_{timestamp}.txt"

    # 获取所有文件夹
    folders = [f for f in os.listdir(current_dir)
              if os.path.isdir(os.path.join(current_dir, f)) and not f.startswith('.')]

    with open(output_filename, 'w', encoding='utf-8') as output_file:
        # 写入文件头信息
        header = f"测试数据对比结果报告\n"
        header += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        header += f"处理文件夹数量: {len(folders)}\n"
        header += f"文件夹列表: {', '.join(folders)}\n"
        header += "="*80 + "\n\n"

        print(header.strip())
        output_file.write(header)

        all_inconsistencies = {}

        for folder in folders:
            folder_path = os.path.join(current_dir, folder)

            # 查找Word和Excel文件
            word_file = None
            excel_file = None

            for file in os.listdir(folder_path):
                if file.endswith('.docx') and not file.startswith('~$'):
                    word_file = os.path.join(folder_path, file)
                elif file.endswith('.xlsx'):
                    excel_file = os.path.join(folder_path, file)

            if not word_file or not excel_file:
                warning_text = f"警告: 文件夹 {folder} 中缺少Word或Excel文件\n"
                print(warning_text.strip())
                output_file.write(warning_text)
                continue

            folder_info = f"\n处理文件夹: {folder}\n"
            folder_info += f"Word文件: {os.path.basename(word_file)}\n"
            folder_info += f"Excel文件: {os.path.basename(excel_file)}\n"

            print(folder_info.strip())
            output_file.write(folder_info)

            # 提取数据
            test_summary_data = extract_test_summary_data(word_file, folder)
            test_process_data = extract_test_process_data(word_file, folder)
            test_case_data = extract_test_case_data(excel_file)

            data_info = f"测试报告汇总数据量: {len(test_summary_data)}\n"
            data_info += f"测试过程数据量: {len(test_process_data)}\n"
            data_info += f"测试用例数据量: {len(test_case_data)}\n"

            print(data_info.strip())
            output_file.write(data_info)

            # 对比数据
            inconsistencies = compare_data(test_summary_data, test_process_data, test_case_data, folder, output_file)

            if inconsistencies:
                all_inconsistencies[folder] = inconsistencies

            # 新增检查功能
            output_file.write("\n--- 附加检查 ---\n")
            print("\n--- 附加检查 ---")

            # 检查封面修订页（LK1A_CDC文件夹跳过）
            if folder != "LK1A_CDC":
                check_version_and_date(word_file, folder, output_file)
            else:
                skip_msg = "跳过封面修订页检查（LK1A_CDC特殊处理）\n"
                print(skip_msg.strip())
                output_file.write(skip_msg)

            # 检查漏洞统计
            check_vulnerability_statistics(word_file, folder, output_file)

        # 总结报告
        summary_header = "\n" + "="*50 + "\n"
        summary_header += "总结报告\n"
        summary_header += "="*50 + "\n"

        print(summary_header.strip())
        output_file.write(summary_header)

        if all_inconsistencies:
            summary_text = "发现不一致的文件夹:\n"
            print(summary_text.strip())
            output_file.write(summary_text)

            for folder, inconsistencies in all_inconsistencies.items():
                folder_summary = f"  {folder}: {len(inconsistencies)} 个不一致项\n"
                print(folder_summary.strip())
                output_file.write(folder_summary)
        else:
            summary_text = "所有文件夹的数据都一致！\n"
            print(summary_text.strip())
            output_file.write(summary_text)

        # 写入文件尾信息
        footer = f"\n报告生成完成！\n"
        footer += f"详细结果已保存到: {output_filename}\n"

        print(footer.strip())
        output_file.write(footer)

    print(f"\n✅ 结果已导出到文件: {output_filename}")

if __name__ == "__main__":
    main()