#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示搜索功能的脚本
"""

from searchexcel import search_excel_and_print_results

def demo_search():
    """演示搜索功能"""
    file_path = "PICU_网络安全威胁分析和风险评估表_V1.0_20240621.xlsx"
    sheet_name = "网络安全需求CS Requirement List"
    
    print("=== Excel搜索功能演示 ===")
    print(f"文件：{file_path}")
    print(f"工作表：{sheet_name}")
    print("搜索列：D列")
    print("结果列：A列")
    print("=" * 50)
    
    # 演示几个搜索示例
    search_examples = [
        "安全启动",
        "访问控制", 
        "真实性",
        "完整性",
        "日志"
    ]
    
    for search_text in search_examples:
        print(f"\n{'='*60}")
        print(f"搜索示例：'{search_text}'")
        print(f"{'='*60}")
        search_excel_and_print_results(file_path, sheet_name, search_text)
        print("\n" + "-"*60)

if __name__ == "__main__":
    demo_search()
