测试数据对比结果报告
生成时间: 2025-09-19 17:37:36
处理文件夹数量: 7
文件夹列表: Fr_camera, LK1A_CDC, LK2A_CCM, LK2A_TCU, PK1B_ADCU, PK1B_L2, PK1B_L4
================================================================================


处理文件夹: Fr_camera
Word文件: 网络安全确认测试报告_【LK2A】_【Fr_camera】_【20250829】.docx
Excel文件: 网络安全确认测试用例_【LK2A】_【Fr_camera】_【20250829】.xlsx
测试报告汇总数据量: 58
测试过程数据量: 59
测试用例数据量: 58

=== 处理文件夹: Fr_camera ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 0 个，低危漏洞 1 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统 。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统 。

检测到的测试轮次: ['一']
检测到的漏洞统计: [('0', '0', '0', '1')]
最终漏洞统计: 超高危0个，高危0个，中危0个，低危1个
报告中的安全等级: S1（A）级，，低安全隐患系统
根据漏洞统计计算的等级: S1级，低安全隐患
漏洞统计信息正确

处理文件夹: LK1A_CDC
Word文件: LK1A_CDC_8295渗透测试报告V2.1.1.docx
Excel文件: 网络安全确认测试用例_【LK1A】_【CDC 8295】_【20250318】.xlsx
测试报告汇总数据量: 96
测试过程数据量: 96
测试用例数据量: 96

=== 处理文件夹: LK1A_CDC ===
  所有数据一致！

--- 附加检查 ---
跳过封面修订页检查（LK1A_CDC特殊处理）
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0个，高危漏洞 6 个，中危漏洞 5 个，低危漏洞 9 个。
受测系统在第二轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0个，高危漏洞 0 个，中危漏洞 3 个，低危漏洞 2 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S2（B）级，中安全隐患系统 。
检测到的测试轮次: ['一', '二']
检测到的漏洞统计: [('0', '6', '5', '9'), ('0', '0', '3', '2')]
最终漏洞统计: 超高危0个，高危0个，中危3个，低危2个
报告中的安全等级: S2（B）级，，中安全隐患系统
根据漏洞统计计算的等级: S2级，中安全隐患
漏洞统计信息正确

处理文件夹: LK2A_CCM
Word文件: 网络安全确认测试报告_【LK2A】_【CCM】_【20250822】.docx
Excel文件: 网络安全确认测试用例_【LK2A】_【CCM】_【20250822】.xlsx
测试报告汇总数据量: 57
测试过程数据量: 57
测试用例数据量: 57

=== 处理文件夹: LK2A_CCM ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 4 个，低危漏洞 7 个。
受测系统在第二轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 0 个，低危漏洞 1 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统。

检测到的测试轮次: ['一', '二']
检测到的漏洞统计: [('0', '0', '4', '7'), ('0', '0', '0', '1')]
最终漏洞统计: 超高危0个，高危0个，中危0个，低危1个
报告中的安全等级: S1（A）级，，低安全隐患系统
根据漏洞统计计算的等级: S1级，低安全隐患
漏洞统计信息正确

处理文件夹: LK2A_TCU
Word文件: 网络安全确认测试报告_【LK2A】_【TCU】_【20250904】.docx
Excel文件: 网络安全确认测试用例_【LK2A】_【TCU】_【20250904】.xlsx
测试报告汇总数据量: 72
测试过程数据量: 72
测试用例数据量: 72

=== 处理文件夹: LK2A_TCU ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 1 个，低危漏洞 6 个。
受测系统在第二轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 0 个，低危漏洞 1 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统 。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统 。

检测到的测试轮次: ['一', '二']
检测到的漏洞统计: [('0', '0', '1', '6'), ('0', '0', '0', '1')]
最终漏洞统计: 超高危0个，高危0个，中危0个，低危1个
报告中的安全等级: S1（A）级，，低安全隐患系统
根据漏洞统计计算的等级: S1级，低安全隐患
漏洞统计信息正确

处理文件夹: PK1B_ADCU
Word文件: 网络安全确认测试报告_【PK1B】_【ADCU】_【20250829】.docx
Excel文件: 网络安全确认测试用例_【PK1B】_【ADCU】_【20250829】.xlsx
测试报告汇总数据量: 101
测试过程数据量: 102
测试用例数据量: 101

=== 处理文件夹: PK1B_ADCU ===
发现不一致项:
  对比: 测试报告汇总 vs 测试过程
  用例编号: /
  测试报告汇总名称: 缺失
  测试过程名称: 多路CAN信息汇总
  ---
  对比: 测试过程 vs 测试用例
  用例编号: /
  测试过程名称: 多路CAN信息汇总
  测试用例名称: 缺失
  ---

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 0 个，低危漏洞 4 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统 。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S1（A）级，低安全隐患系统 。

检测到的测试轮次: ['一']
检测到的漏洞统计: [('0', '0', '0', '4')]
最终漏洞统计: 超高危0个，高危0个，中危0个，低危4个
报告中的安全等级: S1（A）级，，低安全隐患系统
根据漏洞统计计算的等级: S1级，低安全隐患
漏洞统计信息正确

处理文件夹: PK1B_L2
Word文件: 网络安全确认测试报告_【PK1B L2】_【整车】_【20250912】.docx
Excel文件: 网络安全确认测试用例_【PK1B L2】_【整车】_【20250912】.xlsx
测试报告汇总数据量: 80
测试过程数据量: 80
测试用例数据量: 80

=== 处理文件夹: PK1B_L2 ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 1 个，中危漏洞 6 个，低危漏洞 15 个。
受测系统在第二轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 1 个，低危漏洞 0 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为：S2（B）级，中安全隐患系统。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为：S2（B）级，中安全隐患系统。

检测到的测试轮次: ['一', '二']
检测到的漏洞统计: [('0', '1', '6', '15'), ('0', '0', '1', '0')]
最终漏洞统计: 超高危0个，高危0个，中危1个，低危0个
报告中的安全等级: S2（B）级，，中安全隐患系统
根据漏洞统计计算的等级: S2级，中安全隐患
漏洞统计信息正确

处理文件夹: PK1B_L4
Word文件: 网络安全确认测试报告_【PK1B L4】_【整车】_【20250912】.docx
Excel文件: 网络安全确认测试用例_【PK1B L4】_【整车】_【20250912】.xlsx
测试报告汇总数据量: 44
测试过程数据量: 44
测试用例数据量: 44

=== 处理文件夹: PK1B_L4 ===
  所有数据一致！

--- 附加检查 ---
封面修订页格式正确
提取到的漏洞统计内容:
受测系统在第一轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 1 个，中危漏洞 5 个，低危漏洞 13 个。
受测系统在第二轮信息安全测试过程中发现漏洞情况如下：
超高危漏洞 0 个，高危漏洞 0 个，中危漏洞 0 个，低危漏洞 0 个。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S0（-）级，无安全隐患 。
根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统为： S0（-）级，无安全隐患 。

检测到的测试轮次: ['一', '二']
检测到的漏洞统计: [('0', '1', '5', '13'), ('0', '0', '0', '0')]
最终漏洞统计: 超高危0个，高危0个，中危0个，低危0个
报告中的安全等级: S0（-）级，根据系统安全等级判定的相关指标显示，本轮信息安全测试结果所对应的受测试系统
根据漏洞统计计算的等级: S0级，无安全隐患
漏洞统计信息正确

==================================================
总结报告
==================================================
发现不一致的文件夹:
  Fr_camera: 2 个不一致项
  PK1B_ADCU: 2 个不一致项

报告生成完成！
详细结果已保存到: 测试数据对比结果_20250919_173736.txt
